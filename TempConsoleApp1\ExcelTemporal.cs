using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Text.RegularExpressions;
using DateUtil = NPOI.SS.UserModel.DateUtil;

namespace TempConsoleApp1
{
    public enum TemporalKind { None, DateOnly, TimeOnly, DateTime }

    public sealed record TemporalResult(TemporalKind Kind, DateOnly? DateOnly, TimeOnly? TimeOnly, DateTime? DateTime);

    public static class NpoiTemporalHelper
    {
        public static bool TryGetTemporal(ICell cell, out TemporalResult result)
        {
            result = new TemporalResult(TemporalKind.None, null, null, null);
            if (cell == null) return false;

            // 1. 处理公式单元格：用缓存结果类型
            CellType ctype = cell.CellType == CellType.Formula ? cell.CachedFormulaResultType : cell.CellType;
            if (ctype != CellType.Numeric) return false; // 只考虑数值（日期/时间 或 普通数字）

            double raw = cell.NumericCellValue;
            ICellStyle style = cell.CellStyle;
            short idx = style?.DataFormat ?? (short)0;
            string fmt = style?.GetDataFormatString() ?? string.Empty;

            // 2. 权威判定：POI 的识别
            bool isDateFormat = NPOI.SS.UserModel.DateUtil.IsADateFormat(idx, fmt);

            // 3. reserved 索引兜底（23..36），以及常见内置索引兜底
            if (!isDateFormat)
            {
                if ((idx >= 23 && idx <= 36) || IsBuiltInDateIndex(idx))
                    isDateFormat = true;
            }

            if (!isDateFormat)
            {
                // 启用一个更健壮的轻量解析：去除引号/转义/[$-...] 再找 token
                if (LooksLikeDateTimeFormatString(fmt))
                    isDateFormat = true;
            }

            if (!isDateFormat) return false; // 最后判断不是日期/时间

            // 4. 确定 workbook 是否使用 1904 系统（转换 OADate 时要用）
            bool use1904 = false;
            try
            {
                if (cell.Sheet?.Workbook is XSSFWorkbook xw) use1904 = xw.IsDate1904();
                else if (cell.Sheet?.Workbook is HSSFWorkbook hw) use1904 = hw?.IsDate1904() ?? false;
            }
            catch { use1904 = false; }

            // 5. 优先尝试库提供的便捷访问器（如果运行时存在）
            try
            {
                // 反射查找 DateOnlyCellValue/TimeOnlyCellValue（在较新 NPOI 里已加入）
                var t = cell.GetType();
                var propDateOnly = t.GetProperty("DateOnlyCellValue");
                var propTimeOnly = t.GetProperty("TimeOnlyCellValue");
                var propDate = t.GetProperty("DateCellValue");

                if (propDateOnly != null)
                {
                    var v = propDateOnly.GetValue(cell);
                    if (v is DateOnly d)
                    {
                        result = new TemporalResult(TemporalKind.DateOnly, d, null, null);
                        return true;
                    }
                }
                if (propTimeOnly != null)
                {
                    var v = propTimeOnly.GetValue(cell);
                    if (v is TimeOnly tt)
                    {
                        result = new TemporalResult(TemporalKind.TimeOnly, null, tt, null);
                        return true;
                    }
                }
                if (propDate != null)
                {
                    var v = propDate.GetValue(cell);
                    if (v is DateTime dt)
                    {
                        // 根据格式进一步判断要返回的是 DateOnly / TimeOnly / DateTime
                        var (hasDateToken, hasTimeToken) = AnalyzeFormatForDateTime(fmt, idx);
                        if (hasDateToken && hasTimeToken)
                            result = new TemporalResult(TemporalKind.DateTime, null, null, dt);
                        else if (hasDateToken)
                            result = new TemporalResult(TemporalKind.DateOnly, DateOnly.FromDateTime(dt), null, null);
                        else
                            result = new TemporalResult(TemporalKind.TimeOnly, null, TimeOnly.FromDateTime(dt), null);
                        return true;
                    }
                }
            }
            catch
            {
                // 某些 NPOI 版本或 edge-case 会在访问 DateCellValue 时抛异常，继续走兜底
            }

            // 6. 兜底：用底层转换（更可靠）
            // 如果 value 在合法 OADate 范围内，或者小数部分看起来像时间（0<x<1），我们处理
            bool isValid = NPOI.SS.UserModel.DateUtil.IsValidExcelDate(raw) || (raw > -1 && raw < 1);
            if (!isValid) return false;

            DateTime dtFallback = NPOI.SS.UserModel.DateUtil.GetJavaDate(raw, use1904);

            var (hasDate, hasTime) = AnalyzeFormatForDateTime(fmt, idx);
            if (hasDate && hasTime)
            {
                result = new TemporalResult(TemporalKind.DateTime, null, null, dtFallback);
                return true;
            }
            if (hasDate)
            {
                result = new TemporalResult(TemporalKind.DateOnly, DateOnly.FromDateTime(dtFallback), null, null);
                return true;
            }
            // 纯时间（或无法判断但 raw < 1）
            double frac = raw - Math.Truncate(raw);
            if (frac < 0) frac += 1.0;
            var tOnly = TimeOnly.FromTimeSpan(TimeSpan.FromDays(frac));
            result = new TemporalResult(TemporalKind.TimeOnly, null, tOnly, null);
            return true;
        }

        // 简化的内置索引判断（可扩展）
        private static bool IsBuiltInDateIndex(short idx)
            => idx == 14 || idx == 15 || idx == 16 || idx == 17 || idx == 18
               || idx == 19 || idx == 20 || idx == 21 || idx == 22
               || idx == 45 || idx == 46 || idx == 47;

        // 格式字符串轻量判定（去掉引号里文本、转义、[$-...]）
        private static bool LooksLikeDateTimeFormatString(string rawFmt)
        {
            if (string.IsNullOrEmpty(rawFmt)) return false;
            var fmt = rawFmt.ToLowerInvariant();
            int sc = fmt.IndexOf(';');
            if (sc >= 0)
            {
                fmt = fmt.Substring(0, sc);
            }
            fmt = Regex.Replace(fmt, "\"[^\"]*\"", "");    // 去除 "..." 字面量
            fmt = Regex.Replace(fmt, @"\\.", "");         // 去除转义
            fmt = Regex.Replace(fmt, @"\[\$-[^\]]*\]", ""); // 去除区域标记
                                                            // 查找 date/time token
            return Regex.IsMatch(fmt, @"y+|d+|h+|s+|m+|am\/pm|\[h\]");
        }

        // 更精确的格式分析（区分 m 为 月 / 分）
        private static (bool hasDate, bool hasTime) AnalyzeFormatForDateTime(string rawFmt, short idx)
        {
            string fmt = rawFmt?.ToLowerInvariant() ?? "";
            int sc = fmt.IndexOf(';'); if (sc >= 0) fmt = fmt.Substring(0, sc);
            fmt = Regex.Replace(fmt, "\"[^\"]*\"", "");
            fmt = Regex.Replace(fmt, @"\\.", "");
            fmt = Regex.Replace(fmt, @"\[\$-[^\]]*\]", "");
            // 保留 [h] [m] [s]，去掉其他 []
            fmt = Regex.Replace(fmt, @"\[(?![hms])[^]]+\]", "");

            bool hasY = Regex.IsMatch(fmt, "y+");
            bool hasD = Regex.IsMatch(fmt, @"\bd+\b");
            bool hasH = Regex.IsMatch(fmt, @"(\[h+\]|h+)");
            bool hasS = Regex.IsMatch(fmt, @"(\[s+\]|s+)");
            bool hasAmPm = Regex.IsMatch(fmt, @"am\/pm|a\/p");

            // 判断 m 是分钟的证据：与 h/s/: 或 AM/PM 共现
            bool mAsMinute = hasAmPm || hasH || hasS || fmt.Contains(":") || Regex.IsMatch(fmt, @"h+.*m+|m+.*s+");
            bool hasM = Regex.IsMatch(fmt, "m+");
            bool hasMonth = hasM && !mAsMinute;
            bool hasMinute = hasM && mAsMinute;

            bool hasDate = hasY || hasD || hasMonth || IsBuiltInDateIndex(idx);
            bool hasTime = hasH || hasS || hasMinute || IsBuiltInTimeIndex(idx);

            return (hasDate, hasTime);
        }

        private static bool IsBuiltInTimeIndex(short idx)
            => idx == 18 || idx == 19 || idx == 20 || idx == 21 || idx == 45 || idx == 46 || idx == 47;
    }

}
