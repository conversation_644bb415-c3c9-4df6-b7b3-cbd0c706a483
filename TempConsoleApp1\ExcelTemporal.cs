using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Collections.Concurrent;
using System.Reflection;
using System.Text.RegularExpressions;
using DateUtil = NPOI.SS.UserModel.DateUtil;

namespace TempConsoleApp1
{
    public enum TemporalKind { None, DateOnly, TimeOnly, DateTime }

    public sealed record TemporalResult(TemporalKind Kind, DateOnly? DateOnly, TimeOnly? TimeOnly, DateTime? DateTime);

    public static class NpoiTemporalHelper
    {
        // 缓存反射结果以提升性能
        private static readonly ConcurrentDictionary<Type, PropertyInfo?> _dateOnlyPropertyCache = new();
        private static readonly ConcurrentDictionary<Type, PropertyInfo?> _timeOnlyPropertyCache = new();
        private static readonly ConcurrentDictionary<Type, PropertyInfo?> _dateTimePropertyCache = new();

        // 预编译的正则表达式以提升性能
        private static readonly Regex _quotedTextRegex = new("\"[^\"]*\"", RegexOptions.Compiled);
        private static readonly Regex _escapedCharRegex = new(@"\\.", RegexOptions.Compiled);
        private static readonly Regex _localeMarkerRegex = new(@"\[\$-[^\]]*\]", RegexOptions.Compiled);
        private static readonly Regex _nonTimeSquareBracketRegex = new(@"\[(?![hms])[^]]+\]", RegexOptions.Compiled);
        private static readonly Regex _dateTimeTokenRegex = new(@"y+|d+|h+|s+|m+|am\/pm|a\/p|\[h\]|\[m\]|\[s\]", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _yearTokenRegex = new("y+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _dayTokenRegex = new(@"\bd+\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _hourTokenRegex = new(@"(\[h+\]|h+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _secondTokenRegex = new(@"(\[s+\]|s+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _amPmTokenRegex = new(@"am\/pm|a\/p", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _minuteTokenRegex = new("m+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _minuteContextRegex = new(@"h+.*m+|m+.*s+", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        // 内置日期格式索引（完整列表）
        private static readonly HashSet<short> _builtInDateIndexes = new()
        {
            14, 15, 16, 17, 18, 19, 20, 21, 22, // 标准日期格式
            23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, // 保留索引范围
            45, 46, 47 // 额外的时间格式
        };

        // 内置时间格式索引
        private static readonly HashSet<short> _builtInTimeIndexes = new()
        {
            18, 19, 20, 21, 45, 46, 47
        };
        /// <summary>
        /// 尝试从 NPOI 单元格中提取日期/时间数据
        /// </summary>
        /// <param name="cell">NPOI 单元格对象</param>
        /// <param name="result">提取结果</param>
        /// <returns>是否成功提取到日期/时间数据</returns>
        public static bool TryGetTemporal(ICell cell, out TemporalResult result)
        {
            result = new TemporalResult(TemporalKind.None, null, null, null);

            if (!IsValidCell(cell, out var cellInfo))
                return false;

            if (!IsDateTimeFormat(cellInfo.FormatIndex, cellInfo.FormatString))
                return false;

            bool use1904 = GetDateSystem(cell);

            // 优先尝试使用 NPOI 的新版本属性
            if (TryGetTemporalFromReflection(cell, cellInfo.FormatString, cellInfo.FormatIndex, out result))
                return true;

            // 兜底方案：使用底层转换
            return TryGetTemporalFromFallback(cellInfo.NumericValue, cellInfo.FormatString, cellInfo.FormatIndex, use1904, out result);
        }

        /// <summary>
        /// 验证单元格是否有效并提取基本信息
        /// </summary>
        private static bool IsValidCell(ICell cell, out CellInfo cellInfo)
        {
            cellInfo = default;

            if (cell == null)
                return false;

            // 处理公式单元格：使用缓存结果类型
            CellType cellType = cell.CellType == CellType.Formula ? cell.CachedFormulaResultType : cell.CellType;
            if (cellType != CellType.Numeric)
                return false;

            double numericValue = cell.NumericCellValue;
            ICellStyle style = cell.CellStyle;
            short formatIndex = style?.DataFormat ?? 0;
            string formatString = style?.GetDataFormatString() ?? string.Empty;

            cellInfo = new CellInfo(numericValue, formatIndex, formatString);
            return true;
        }

        /// <summary>
        /// 判断格式是否为日期/时间格式
        /// </summary>
        private static bool IsDateTimeFormat(short formatIndex, string formatString)
        {
            // 1. 使用 NPOI 的权威判定
            if (DateUtil.IsADateFormat(formatIndex, formatString))
                return true;

            // 2. 检查内置日期格式索引
            if (_builtInDateIndexes.Contains(formatIndex))
                return true;

            // 3. 通过格式字符串模式匹配判定
            return ContainsDateTimeTokens(formatString);
        }

        /// <summary>
        /// 获取工作簿的日期系统（1900 或 1904）
        /// </summary>
        private static bool GetDateSystem(ICell cell)
        {
            try
            {
                return cell.Sheet?.Workbook switch
                {
                    XSSFWorkbook xssf => xssf.IsDate1904(),
                    HSSFWorkbook hssf => hssf.IsDate1904(),
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 尝试通过反射获取 NPOI 新版本的日期/时间属性
        /// </summary>
        private static bool TryGetTemporalFromReflection(ICell cell, string formatString, short formatIndex, out TemporalResult result)
        {
            result = default;

            try
            {
                Type cellType = cell.GetType();

                // 尝试获取 DateOnlyCellValue
                var dateOnlyProp = GetCachedProperty(cellType, "DateOnlyCellValue", _dateOnlyPropertyCache);
                if (dateOnlyProp?.GetValue(cell) is DateOnly dateOnly)
                {
                    result = new TemporalResult(TemporalKind.DateOnly, dateOnly, null, null);
                    return true;
                }

                // 尝试获取 TimeOnlyCellValue
                var timeOnlyProp = GetCachedProperty(cellType, "TimeOnlyCellValue", _timeOnlyPropertyCache);
                if (timeOnlyProp?.GetValue(cell) is TimeOnly timeOnly)
                {
                    result = new TemporalResult(TemporalKind.TimeOnly, null, timeOnly, null);
                    return true;
                }

                // 尝试获取 DateCellValue
                var dateTimeProp = GetCachedProperty(cellType, "DateCellValue", _dateTimePropertyCache);
                if (dateTimeProp?.GetValue(cell) is DateTime dateTime)
                {
                    var (hasDate, hasTime) = AnalyzeFormatTokens(formatString, formatIndex);

                    if (hasDate && hasTime)
                        result = new TemporalResult(TemporalKind.DateTime, null, null, dateTime);
                    else if (hasDate)
                        result = new TemporalResult(TemporalKind.DateOnly, DateOnly.FromDateTime(dateTime), null, null);
                    else
                        result = new TemporalResult(TemporalKind.TimeOnly, null, TimeOnly.FromDateTime(dateTime), null);

                    return true;
                }
            }
            catch
            {
                // 反射失败时继续使用兜底方案
            }

            return false;
        }

        /// <summary>
        /// 获取缓存的属性信息
        /// </summary>
        private static PropertyInfo? GetCachedProperty(Type type, string propertyName, ConcurrentDictionary<Type, PropertyInfo?> cache)
        {
            return cache.GetOrAdd(type, t => t.GetProperty(propertyName));
        }

        /// <summary>
        /// 兜底方案：使用底层转换获取日期/时间
        /// </summary>
        private static bool TryGetTemporalFromFallback(double numericValue, string formatString, short formatIndex, bool use1904, out TemporalResult result)
        {
            result = default;

            // 验证数值是否为有效的 Excel 日期，或者是纯时间值（0-1之间）
            bool isValidDate = DateUtil.IsValidExcelDate(numericValue) || (numericValue > -1 && numericValue < 1);
            if (!isValidDate)
                return false;

            DateTime dateTime = DateUtil.GetJavaDate(numericValue, use1904);
            var (hasDate, hasTime) = AnalyzeFormatTokens(formatString, formatIndex);

            if (hasDate && hasTime)
            {
                result = new TemporalResult(TemporalKind.DateTime, null, null, dateTime);
            }
            else if (hasDate)
            {
                result = new TemporalResult(TemporalKind.DateOnly, DateOnly.FromDateTime(dateTime), null, null);
            }
            else
            {
                // 纯时间：从小数部分计算时间
                double fraction = numericValue - Math.Truncate(numericValue);
                if (fraction < 0) fraction += 1.0;
                TimeOnly timeOnly = TimeOnly.FromTimeSpan(TimeSpan.FromDays(fraction));
                result = new TemporalResult(TemporalKind.TimeOnly, null, timeOnly, null);
            }

            return true;
        }

        /// <summary>
        /// 检查格式字符串是否包含日期/时间标记
        /// </summary>
        private static bool ContainsDateTimeTokens(string formatString)
        {
            if (string.IsNullOrEmpty(formatString))
                return false;

            string cleanedFormat = CleanFormatString(formatString);
            return _dateTimeTokenRegex.IsMatch(cleanedFormat);
        }

        /// <summary>
        /// 分析格式字符串中的日期和时间标记
        /// </summary>
        private static (bool hasDate, bool hasTime) AnalyzeFormatTokens(string formatString, short formatIndex)
        {
            string cleanedFormat = CleanFormatString(formatString);

            bool hasYear = _yearTokenRegex.IsMatch(cleanedFormat);
            bool hasDay = _dayTokenRegex.IsMatch(cleanedFormat);
            bool hasHour = _hourTokenRegex.IsMatch(cleanedFormat);
            bool hasSecond = _secondTokenRegex.IsMatch(cleanedFormat);
            bool hasAmPm = _amPmTokenRegex.IsMatch(cleanedFormat);

            // 精确判断 m 是月份还是分钟
            bool hasMinuteToken = _minuteTokenRegex.IsMatch(cleanedFormat);
            bool isMinuteContext = hasAmPm || hasHour || hasSecond || cleanedFormat.Contains(':') || _minuteContextRegex.IsMatch(cleanedFormat);

            bool hasMonth = hasMinuteToken && !isMinuteContext;
            bool hasMinute = hasMinuteToken && isMinuteContext;

            bool hasDate = hasYear || hasDay || hasMonth || _builtInDateIndexes.Contains(formatIndex);
            bool hasTime = hasHour || hasSecond || hasMinute || _builtInTimeIndexes.Contains(formatIndex);

            return (hasDate, hasTime);
        }

        /// <summary>
        /// 清理格式字符串，移除引号、转义字符和区域标记
        /// </summary>
        private static string CleanFormatString(string formatString)
        {
            if (string.IsNullOrEmpty(formatString))
                return string.Empty;

            // 转换为小写并取第一个分号前的部分
            string cleaned = formatString.ToLowerInvariant();
            int semicolonIndex = cleaned.IndexOf(';');
            if (semicolonIndex >= 0)
                cleaned = cleaned.Substring(0, semicolonIndex);

            // 移除各种格式标记
            cleaned = _quotedTextRegex.Replace(cleaned, "");           // 移除 "..." 字面量
            cleaned = _escapedCharRegex.Replace(cleaned, "");          // 移除转义字符
            cleaned = _localeMarkerRegex.Replace(cleaned, "");         // 移除区域标记 [$-...]
            cleaned = _nonTimeSquareBracketRegex.Replace(cleaned, ""); // 移除非时间的方括号标记

            return cleaned;
        }

        /// <summary>
        /// 单元格信息结构体
        /// </summary>
        private readonly struct CellInfo
        {
            public readonly double NumericValue;
            public readonly short FormatIndex;
            public readonly string FormatString;

            public CellInfo(double numericValue, short formatIndex, string formatString)
            {
                NumericValue = numericValue;
                FormatIndex = formatIndex;
                FormatString = formatString;
            }
        }
    }

}
